#!/usr/bin/env python3
"""
Simple test version to debug the PRD generator
"""

import os
import asyncio
from langchain_google_genai import ChatGoogleGenerativeAI

async def test_basic_functionality():
    """Test basic LangChain functionality"""
    print("🔧 Testing PRD Generator Components...")
    
    try:
        # Test 1: Environment
        print("✅ 1. Python environment: OK")
        
        # Test 2: API Key
        api_key = os.environ.get("GOOGLE_API_KEY", "AIzaSyCJaADIHnUd3TmZDfyeh2JKk_k8WO6t7JI")
        if api_key:
            print("✅ 2. API Key found: OK")
        else:
            print("❌ 2. API Key missing")
            return
        
        # Test 3: LangChain Import
        print("✅ 3. LangChain imports: OK")
        
        # Test 4: LLM Initialization
        try:
            llm = ChatGoogleGenerativeAI(
                model="gemini-1.5-flash",
                google_api_key=api_key,
                temperature=0.7
            )
            print("✅ 4. LLM initialization: OK")
        except Exception as e:
            print(f"❌ 4. LLM initialization failed: {e}")
            return
        
        # Test 5: Simple API call
        try:
            print("🤔 5. Testing API call...")
            result = await llm.ainvoke("Say hello in one word")
            print(f"✅ 5. API call successful: {result.content}")
        except Exception as e:
            print(f"❌ 5. API call failed: {e}")
            if "quota" in str(e).lower():
                print("💡 This is likely a quota/rate limit issue. The code structure is correct.")
                print("💡 Try with a fresh API key or wait for quota reset.")
            return
        
        print("\n🎉 All tests passed! The PRD generator should work with a valid API key.")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

async def test_conversation_structure():
    """Test the conversation structure without API calls"""
    print("\n🔧 Testing Conversation Structure...")
    
    try:
        # Import the main classes
        from prd import ConversationTreeBuilder, PRDDocument
        
        # Test conversation tree
        tree = ConversationTreeBuilder.build_tree()
        print(f"✅ Conversation tree built with {len(tree)} nodes")
        
        # Test PRD document
        prd = PRDDocument()
        print(f"✅ PRD document initialized with {len(prd.model_fields)} sections")
        
        # Test some nodes
        start_node = tree.get("start")
        if start_node:
            print(f"✅ Start node found: {start_node.name}")
        
        print("✅ Conversation structure is valid!")
        
    except Exception as e:
        print(f"❌ Conversation structure error: {e}")

def main():
    """Run all tests"""
    print("🚀 PRD Generator Diagnostic Test")
    print("=" * 40)
    
    try:
        # Test basic functionality
        asyncio.run(test_basic_functionality())
        
        # Test conversation structure
        asyncio.run(test_conversation_structure())
        
        print("\n" + "=" * 40)
        print("🎯 Diagnosis Complete!")
        print("\nIf API tests failed due to quota limits, try:")
        print("1. Get a new API key from https://makersuite.google.com/app/apikey")
        print("2. Wait for quota reset (usually 24 hours)")
        print("3. Use the demo version: python prd_demo.py")
        
    except KeyboardInterrupt:
        print("\n👋 Test interrupted")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")

if __name__ == "__main__":
    main()
